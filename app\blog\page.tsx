"use client";

import Link from "next/link";
import { ArrowRight } from "lucide-react";
import Image from "next/image";
import Card from "../components/card";

// Sample blog posts data (in a real app, this would come from a CMS or database)
const blogPosts = [
	{
		id: "getting-started-with-nextjs",
		title: "Getting Started with Next.js 15",
		excerpt: "Learn how to build modern web applications with Next.js 15",
		date: "2024-03-20",
		image: "https://images.unsplash.com/photo-1555066931-4365d14bab8c",
		tags: ["Next.js", "React", "Web Development"],
		readTime: "5 min read",
	},
	{
		id: "mastering-react-hooks",
		title: "Mastering React Hooks",
		excerpt: "A comprehensive guide to React Hooks and their practical applications",
		date: "2024-03-19",
		image: "https://images.unsplash.com/photo-1633356122544-f134324a6cee",
		tags: ["React", "JavaScript", "Web Development"],
		readTime: "8 min read",
	},
];

export default function BlogPage() {
	return (
		<div className="min-h-screen bg-black">
			<div className="max-w-5xl mx-auto px-6 py-20">
				{/* Header */}
				<div className="mb-16 text-center">
					<h1 className="text-4xl font-bold text-white mb-4">Blog</h1>
					<p className="text-zinc-400 text-lg max-w-2xl mx-auto">
						Thoughts, tutorials, and insights about web development and technology.
					</p>
				</div>

				{/* Blog Posts Grid */}
				<div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
					{blogPosts.map((post, index) => (
						<Card
							key={post.id}
							type="blog"
							title={post.title}
							description={post.excerpt}
							image={post.image}
							link={`/blog/${post.id}`}
							tags={post.tags}
							date={post.date}
							readTime={post.readTime}
							index={index}
						/>
					))}
				</div>
			</div>
		</div>
	);
}
