import { Project } from "../data/projects";
import { BlogPost } from "../data/blog-posts";

export function filterProjects(
	projects: Project[],
	searchQuery: string,
	selectedTags: string[]
): Project[] {
	return projects.filter((project) => {
		const matchesSearch = searchQuery
			? project.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
			  project.description.toLowerCase().includes(searchQuery.toLowerCase())
			: true;

		const matchesTags =
			selectedTags.length === 0 || selectedTags.every((tag) => project.technologies.includes(tag));

		return matchesSearch && matchesTags;
	});
}

export function filterBlogPosts(
	posts: BlogPost[],
	searchQuery: string,
	selectedTags: string[]
): BlogPost[] {
	return posts.filter((post) => {
		const matchesSearch = searchQuery
			? post.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
			  post.excerpt.toLowerCase().includes(searchQuery.toLowerCase())
			: true;

		const matchesTags =
			selectedTags.length === 0 || selectedTags.every((tag) => post.tags.includes(tag));

		return matchesSearch && matchesTags;
	});
}

export function getAllTags(items: (Project | BlogPost)[]): string[] {
	const tags = new Set<string>();
	items.forEach((item) => {
		if ("technologies" in item) {
			item.technologies.forEach((tech) => tags.add(tech));
		} else if ("tags" in item) {
			item.tags.forEach((tag) => tags.add(tag));
		}
	});
	return Array.from(tags).sort();
}
