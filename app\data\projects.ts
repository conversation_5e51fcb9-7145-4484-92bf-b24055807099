export interface Project {
	id: number;
	title: string;
	description: string;
	image: string;
	technologies: string[];
	githubUrl: string;
	liveUrl: string;
}

export const projects: Project[] = [
	{
		id: 1,
		title: "E-Commerce Platform",
		description: "A full-stack e-commerce solution with payment processing",
		image:
			"https://images.unsplash.com/photo-1515168833906-d2a3b82b3029?auto=format&fit=crop&w=400&q=80",
		technologies: ["Next.js", "TypeScript", "Stripe", "PostgreSQL"],
		githubUrl: "https://github.com/yourusername/ecommerce-platform",
		liveUrl: "https://ecommerce.example.com",
	},
	{
		id: 2,
		title: "Task Management App",
		description: "Collaborative task management with real-time updates",
		image:
			"https://images.unsplash.com/photo-1461749280684-dccba630e2f6?auto=format&fit=crop&w=400&q=80",
		technologies: ["React", "Node.js", "Socket.io", "MongoDB"],
		githubUrl: "https://github.com/yourusername/task-manager",
		liveUrl: "https://tasks.example.com",
	},
	{
		id: 3,
		title: "Weather Dashboard",
		description: "Beautiful weather dashboard with interactive charts",
		image:
			"https://images.unsplash.com/photo-1506744038136-46273834b3fb?auto=format&fit=crop&w=400&q=80",
		technologies: ["React", "Chart.js", "OpenWeather API"],
		githubUrl: "https://github.com/yourusername/weather-dashboard",
		liveUrl: "https://weather.example.com",
	},
	{
		id: 4,
		title: "Portfolio Website",
		description: "Modern portfolio with smooth animations",
		image:
			"https://images.unsplash.com/photo-1465101046530-73398c7f28ca?auto=format&fit=crop&w=400&q=80",
		technologies: ["Next.js", "Tailwind CSS", "Framer Motion"],
		githubUrl: "https://github.com/yourusername/portfolio",
		liveUrl: "https://portfolio.example.com",
	},
	{
		id: 5,
		title: "AI Chat Application",
		description: "Intelligent chat app powered by OpenAI",
		image:
			"https://images.unsplash.com/photo-1519389950473-47ba0277781c?auto=format&fit=crop&w=400&q=80",
		technologies: ["Next.js", "OpenAI API", "Prisma"],
		githubUrl: "https://github.com/yourusername/ai-chat-app",
		liveUrl: "https://aichat.example.com",
	},
	{
		id: 6,
		title: "Expense Tracker",
		description: "Personal finance management with insights",
		image:
			"https://images.unsplash.com/photo-1464983953574-0892a716854b?auto=format&fit=crop&w=400&q=80",
		technologies: ["React Native", "Firebase", "Chart.js"],
		githubUrl: "https://github.com/yourusername/expense-tracker",
		liveUrl: "https://expenses.example.com",
	},
	{
		id: 7,
		title: "Recipe Finder",
		description: "Discover and save your favorite recipes with ease",
		image:
			"https://images.unsplash.com/photo-1504674900247-0877df9cc836?auto=format&fit=crop&w=400&q=80",
		technologies: ["Vue.js", "Express", "MongoDB"],
		githubUrl: "https://github.com/yourusername/recipe-finder",
		liveUrl: "https://recipes.example.com",
	},
	{
		id: 8,
		title: "Fitness Tracker",
		description: "Track workouts, nutrition, and progress visually",
		image:
			"https://images.unsplash.com/photo-1518611012118-696072aa579a?auto=format&fit=crop&w=400&q=80",
		technologies: ["Flutter", "Firebase", "Dart"],
		githubUrl: "https://github.com/yourusername/fitness-tracker",
		liveUrl: "https://fit.example.com",
	},
];
